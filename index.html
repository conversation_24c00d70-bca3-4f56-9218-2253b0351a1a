<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZOIP - Professional Realtime Notepad</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&family=SF+Mono:wght@400;500;600&display=swap');

        * {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .font-mono {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        /* Authentic GitHub color palette */
        :root {
            --gh-canvas-default: #0d1117;
            --gh-canvas-subtle: #161b22;
            --gh-canvas-inset: #010409;
            --gh-border-default: #30363d;
            --gh-border-muted: #21262d;
            --gh-fg-default: #e6edf3;
            --gh-fg-muted: #7d8590;
            --gh-fg-subtle: #656d76;
            --gh-fg-on-emphasis: #ffffff;
            --gh-accent-emphasis: #1f6feb;
            --gh-accent-muted: rgba(31, 111, 235, 0.15);
            --gh-accent-subtle: rgba(31, 111, 235, 0.05);
            --gh-success-emphasis: #238636;
            --gh-success-muted: rgba(35, 134, 54, 0.15);
            --gh-danger-emphasis: #da3633;
            --gh-danger-muted: rgba(218, 54, 51, 0.15);
            --gh-warning-emphasis: #9a6700;
            --gh-warning-muted: rgba(154, 103, 0, 0.15);
            --gh-neutral-emphasis: #6e7681;
            --gh-neutral-muted: rgba(110, 118, 129, 0.1);
        }

        .github-bg {
            background-color: var(--gh-canvas-default);
        }

        .github-bg-subtle {
            background-color: var(--gh-canvas-subtle);
        }

        .github-border {
            border-color: var(--gh-border-default);
        }

        .github-border-muted {
            border-color: var(--gh-border-muted);
        }

        .github-text {
            color: var(--gh-fg-default);
        }

        .github-text-muted {
            color: var(--gh-fg-muted);
        }

        .github-text-subtle {
            color: var(--gh-fg-subtle);
        }

        .apple-blur {
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
        }

        .apple-shadow {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
        }

        .apple-shadow-lg {
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .pulse-dot {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }

        .typing-indicator {
            background: linear-gradient(90deg, var(--gh-success-emphasis), #2ea043, var(--gh-success-emphasis));
            background-size: 200% 200%;
            animation: gradient 2s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: var(--gh-canvas-subtle);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--gh-border-default);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--gh-fg-subtle);
        }

        .sidebar-item {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-item:hover {
            background-color: var(--gh-accent-muted);
            transform: translateX(2px);
        }

        .sidebar-item.active {
            background-color: var(--gh-accent-muted);
            border-right: 2px solid var(--gh-accent-emphasis);
        }

        .filter-tab {
            position: relative;
        }

        .filter-tab.active {
            color: var(--gh-fg-default);
            background-color: var(--gh-accent-muted);
        }

        .filter-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--gh-accent-emphasis);
            border-radius: 1px;
        }

        .github-dropdown {
            background-color: var(--gh-canvas-subtle);
            border: 1px solid var(--gh-border-default);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .github-dropdown-item {
            color: var(--gh-fg-default);
            transition: background-color 0.1s ease;
        }

        .github-dropdown-item:hover {
            background-color: var(--gh-accent-muted);
        }

        .starred {
            color: var(--gh-warning-emphasis) !important;
        }

        .apple-button {
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateZ(0);
        }

        .apple-button:hover {
            transform: translateY(-1px);
        }

        .apple-button:active {
            transform: translateY(0);
        }

        .notepad-fade-in {
            animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="github-bg min-h-screen flex overflow-hidden">
    <!-- Sidebar -->
    <div class="github-bg-subtle border-r github-border w-80 flex flex-col apple-shadow">
        <!-- Sidebar Header -->
        <div class="p-6 border-b github-border-muted">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center apple-shadow">
                    <i class="fas fa-code text-white text-lg"></i>
                </div>
                <div class="flex-1">
                    <h1 class="text-2xl font-bold github-text">ZOIP</h1>
                    <p class="text-sm github-text-muted">Realtime Collaboration</p>
                </div>
                <!-- GitHub-style settings button -->
                <button id="settingsBtn" class="p-2 github-text-muted hover:github-text rounded-lg hover:bg-gray-800/50 transition-colors" title="Settings">
                    <i class="fas fa-cog text-sm"></i>
                </button>
            </div>

            <!-- GitHub-style search bar -->
            <div class="relative mb-4">
                <input
                    id="searchNotepads"
                    type="text"
                    class="w-full px-3 py-2 pl-8 github-bg border github-border rounded-lg github-text text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Search notepads..."
                />
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 github-text-muted text-xs"></i>
            </div>

            <!-- Add New Notepad Button - GitHub style -->
            <button id="addNotepadBtn" class="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg apple-button font-medium text-sm">
                <i class="fas fa-plus text-xs"></i>
                <span>New notepad</span>
            </button>
        </div>

        <!-- Notepads List -->
        <div class="flex-1 overflow-y-auto scrollbar-thin">
            <div class="p-4">
                <!-- GitHub-style filter tabs -->
                <div class="flex items-center space-x-1 mb-4">
                    <button id="filterAll" class="filter-tab active px-3 py-1 text-sm rounded-lg github-text-muted hover:github-text transition-colors">
                        All <span id="allCount" class="ml-1 px-1.5 py-0.5 bg-gray-700 rounded-full text-xs">0</span>
                    </button>
                    <button id="filterRecent" class="filter-tab px-3 py-1 text-sm rounded-lg github-text-muted hover:github-text transition-colors">
                        Recent
                    </button>
                    <button id="filterStarred" class="filter-tab px-3 py-1 text-sm rounded-lg github-text-muted hover:github-text transition-colors">
                        <i class="fas fa-star text-xs mr-1"></i>Starred
                    </button>
                </div>

                <div class="text-xs font-semibold github-text-subtle uppercase tracking-wider mb-3 px-2 flex items-center justify-between">
                    <span>Notepads</span>
                    <!-- GitHub-style sort dropdown -->
                    <select id="sortNotepads" class="github-bg border github-border rounded text-xs github-text-muted focus:outline-none focus:ring-1 focus:ring-blue-500">
                        <option value="modified">Last modified</option>
                        <option value="name">Name</option>
                        <option value="created">Created</option>
                    </select>
                </div>
                <div id="notepadsList" class="space-y-1">
                    <!-- Notepads will be dynamically added here -->
                </div>
            </div>
        </div>

        <!-- Sidebar Footer - GitHub style -->
        <div class="p-4 border-t github-border-muted github-bg-subtle">
            <!-- Connection Status -->
            <div class="flex items-center justify-between text-sm github-text-muted mb-3">
                <div class="flex items-center space-x-2">
                    <div id="connectionStatus" class="w-2 h-2 rounded-full bg-red-500 pulse-dot"></div>
                    <span id="statusText">Connecting...</span>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-users text-xs"></i>
                    <span id="activeUsers">0</span> online
                </div>
            </div>

            <!-- GitHub-style stats -->
            <div class="grid grid-cols-2 gap-2 text-xs">
                <div class="flex items-center space-x-2 p-2 github-bg rounded border github-border-muted">
                    <i class="fas fa-clock github-text-muted"></i>
                    <div>
                        <div class="github-text-muted">Uptime</div>
                        <div class="github-text font-medium" id="uptime">00:00</div>
                    </div>
                </div>
                <div class="flex items-center space-x-2 p-2 github-bg rounded border github-border-muted">
                    <i class="fas fa-sync-alt github-text-muted"></i>
                    <div>
                        <div class="github-text-muted">Synced</div>
                        <div class="github-text font-medium" id="syncStatus">Yes</div>
                    </div>
                </div>
            </div>

            <!-- GitHub-style user info -->
            <div class="mt-3 p-2 github-bg rounded border github-border-muted">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
                        U
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="github-text text-sm font-medium truncate" id="currentUser">Anonymous User</div>
                        <div class="github-text-muted text-xs">ID: <span id="userIdDisplay"></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col">
        <!-- Top Bar - GitHub style -->
        <div class="github-bg-subtle border-b github-border px-6 py-3 apple-blur">
            <div class="flex items-center justify-between">
                <!-- GitHub-style breadcrumb -->
                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-2 github-text-muted text-sm">
                        <i class="fas fa-code"></i>
                        <span>ZOIP</span>
                        <i class="fas fa-chevron-right text-xs"></i>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-file-alt github-text-muted"></i>
                        <input
                            id="notepadTitle"
                            class="text-lg font-semibold github-text bg-transparent border-none outline-none focus:bg-gray-800/50 px-2 py-1 rounded"
                            value="Untitled"
                            placeholder="Notepad name..."
                        />
                        <!-- GitHub-style star button -->
                        <button id="starBtn" class="p-1 github-text-muted hover:github-text rounded transition-colors" title="Star this notepad">
                            <i class="far fa-star"></i>
                        </button>
                    </div>
                </div>

                <!-- GitHub-style action buttons -->
                <div class="flex items-center space-x-2">
                    <!-- Save status -->
                    <div class="flex items-center space-x-2 text-sm github-text-muted px-3 py-1 github-bg rounded border github-border-muted">
                        <i class="fas fa-save text-green-500 text-xs"></i>
                        <span>Saved <span id="lastSaved" class="text-green-500">never</span></span>
                    </div>

                    <!-- GitHub-style dropdown -->
                    <div class="relative">
                        <button id="moreActionsBtn" class="px-3 py-1 github-text-muted hover:github-text rounded border github-border-muted hover:border-gray-600 transition-colors text-sm">
                            <i class="fas fa-ellipsis-h mr-1"></i>
                            More
                        </button>
                        <div id="moreActionsMenu" class="hidden absolute right-0 mt-2 w-48 github-bg-subtle border github-border rounded-lg shadow-lg z-10">
                            <button id="exportBtn" class="w-full text-left px-4 py-2 text-sm github-text hover:bg-gray-800 rounded-t-lg">
                                <i class="fas fa-download mr-2"></i>Export as Markdown
                            </button>
                            <button id="shareBtn" class="w-full text-left px-4 py-2 text-sm github-text hover:bg-gray-800">
                                <i class="fas fa-share mr-2"></i>Share Link
                            </button>
                            <hr class="border-gray-700 my-1">
                            <button id="clearBtn" class="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-500/10 rounded-b-lg">
                                <i class="fas fa-trash-alt mr-2"></i>Clear Content
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notepad Area -->
        <div class="flex-1 relative">

            <!-- Typing Indicator -->
            <div id="typingIndicator" class="absolute top-6 right-6 hidden items-center space-x-2 px-3 py-2 typing-indicator rounded-lg text-white text-sm font-medium z-10 apple-shadow">
                <div class="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                <span>Someone is typing...</span>
            </div>

            <!-- Full Screen Textarea -->
            <textarea
                id="notepad"
                class="w-full h-full p-8 github-bg github-text resize-none focus:outline-none border-none text-lg leading-relaxed font-mono scrollbar-thin notepad-fade-in"
                placeholder="# Welcome to ZOIP

Start writing your notes here. Everything is automatically saved and synchronized in real-time across all connected users.

## Features:
- 🚀 Real-time collaboration
- 💾 Auto-save functionality
- 🎨 GitHub-inspired design
- 📱 Apple-like aesthetics
- 📝 Multiple notepad support

Happy writing!"
                spellcheck="false"
            ></textarea>
        </div>
    </div>



    <!-- Delete Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black/50 apple-blur hidden items-center justify-center z-50">
        <div class="github-bg-subtle border github-border rounded-xl p-6 w-96 mx-4 apple-shadow-lg">
            <div class="text-center">
                <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold github-text mb-2">Delete Notepad</h3>
                <p class="github-text-muted mb-2">Are you sure you want to delete "<span id="deleteNotepadName" class="font-medium github-text"></span>"?</p>
                <p class="github-text-subtle text-sm mb-6">This action cannot be undone and all content will be permanently lost.</p>
                <div class="flex space-x-3">
                    <button id="cancelDelete" class="flex-1 px-4 py-2 github-text-muted hover:github-text rounded-lg apple-button">
                        Cancel
                    </button>
                    <button id="confirmDelete" class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg apple-button">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Firebase Scripts -->
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.9.1/firebase-app.js";
        import { getDatabase, ref, set, onValue, push, serverTimestamp, onDisconnect } from "https://www.gstatic.com/firebasejs/11.9.1/firebase-database.js";

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyA7I_pIf8vRsschdZ8vjdWTb0UTLf3iCzI",
            authDomain: "mamunotepad.firebaseapp.com",
            databaseURL: "https://mamunotepad-default-rtdb.firebaseio.com",
            projectId: "mamunotepad",
            storageBucket: "mamunotepad.firebasestorage.app",
            messagingSenderId: "553410626492",
            appId: "1:553410626492:web:6847d1e7a8eb4d9ceb61b9",
            measurementId: "G-2JWTRV27GS"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);

        // Generate unique user ID
        const userId = 'user_' + Math.random().toString(36).substr(2, 9);

        // Current notepad state
        let currentNotepadId = 'default';
        let notepads = {};
        let starredNotepads = new Set();
        let currentFilter = 'all';
        let currentSort = 'modified';
        let searchQuery = '';
        let startTime = Date.now();

        // DOM elements
        const notepad = document.getElementById('notepad');
        const connectionStatus = document.getElementById('connectionStatus');
        const statusText = document.getElementById('statusText');
        const activeUsers = document.getElementById('activeUsers');
        const lastSaved = document.getElementById('lastSaved');
        const clearBtn = document.getElementById('clearBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const notepadTitle = document.getElementById('notepadTitle');
        const addNotepadBtn = document.getElementById('addNotepadBtn');
        const notepadsList = document.getElementById('notepadsList');
        const deleteModal = document.getElementById('deleteModal');
        const deleteNotepadName = document.getElementById('deleteNotepadName');
        const confirmDelete = document.getElementById('confirmDelete');
        const cancelDelete = document.getElementById('cancelDelete');

        // New GitHub-style elements
        const searchNotepads = document.getElementById('searchNotepads');
        const filterAll = document.getElementById('filterAll');
        const filterRecent = document.getElementById('filterRecent');
        const filterStarred = document.getElementById('filterStarred');
        const sortNotepads = document.getElementById('sortNotepads');
        const allCount = document.getElementById('allCount');
        const starBtn = document.getElementById('starBtn');
        const moreActionsBtn = document.getElementById('moreActionsBtn');
        const moreActionsMenu = document.getElementById('moreActionsMenu');
        const exportBtn = document.getElementById('exportBtn');
        const shareBtn = document.getElementById('shareBtn');
        const uptime = document.getElementById('uptime');
        const syncStatus = document.getElementById('syncStatus');
        const currentUser = document.getElementById('currentUser');
        const userIdDisplay = document.getElementById('userIdDisplay');

        // Firebase references
        const notepadsRef = ref(database, 'notepads');
        const usersRef = ref(database, 'users');
        const userRef = ref(database, `users/${userId}`);

        // Dynamic references based on current notepad
        let currentNotepadRef, currentUsersRef, currentActivityRef;

        let isUpdatingFromFirebase = false;
        let saveTimeout;
        let currentDeleteId = null;

        // Initialize notepads system
        function initializeNotepads() {
            // Create default notepad if it doesn't exist
            const defaultNotepad = {
                id: 'default',
                name: 'Welcome to ZOIP',
                content: `# Welcome to ZOIP 🚀

## Your GitHub-Style Collaborative Notepad

This is your first notepad! ZOIP brings the familiar GitHub experience to collaborative note-taking.

### Features:
- 🔄 **Real-time collaboration** - See changes instantly
- 🔍 **Search & filter** - Find your notes quickly
- ⭐ **Star favorites** - Mark important notepads
- 📱 **Responsive design** - Works on all devices
- 🎨 **GitHub theme** - Familiar and professional

### Getting Started:
1. Click the "New notepad" button to create more notepads
2. Use the search bar to find specific notepads
3. Star important notepads for quick access
4. Share the URL with others for collaboration

Happy writing! ✨`,
                createdAt: Date.now(),
                lastModified: Date.now(),
                starred: false
            };

            set(ref(database, `notepads/default`), defaultNotepad);
            switchToNotepad('default');
            loadNotepadsList();
            initializeGitHubFeatures();
        }

        // Initialize GitHub-style features
        function initializeGitHubFeatures() {
            // Set user info
            userIdDisplay.textContent = userId.substring(5, 12);
            currentUser.textContent = `User ${userId.substring(5, 8)}`;

            // Start uptime counter
            updateUptime();
            setInterval(updateUptime, 1000);

            // Load starred notepads from localStorage
            const saved = localStorage.getItem('zoip-starred');
            if (saved) {
                starredNotepads = new Set(JSON.parse(saved));
            }
        }

        // Update uptime display
        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            uptime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update dynamic references based on current notepad
        function updateReferences(notepadId) {
            currentNotepadRef = ref(database, `notepads/${notepadId}/content`);
            currentUsersRef = ref(database, `notepads/${notepadId}/users`);
            currentActivityRef = ref(database, `notepads/${notepadId}/activity`);
        }

        // Load notepads list
        function loadNotepadsList() {
            onValue(notepadsRef, (snapshot) => {
                const notepadData = snapshot.val();
                if (notepadData) {
                    notepads = notepadData;
                    renderNotepadsList();
                }
            });
        }

        // Render notepads in sidebar with GitHub-style filtering
        function renderNotepadsList() {
            let filteredNotepads = Object.values(notepads);

            // Apply search filter
            if (searchQuery) {
                filteredNotepads = filteredNotepads.filter(notepad =>
                    notepad.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (notepad.content && notepad.content.toLowerCase().includes(searchQuery.toLowerCase()))
                );
            }

            // Apply category filter
            switch (currentFilter) {
                case 'recent':
                    const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
                    filteredNotepads = filteredNotepads.filter(notepad => notepad.lastModified > weekAgo);
                    break;
                case 'starred':
                    filteredNotepads = filteredNotepads.filter(notepad => starredNotepads.has(notepad.id));
                    break;
            }

            // Apply sorting
            switch (currentSort) {
                case 'name':
                    filteredNotepads.sort((a, b) => a.name.localeCompare(b.name));
                    break;
                case 'created':
                    filteredNotepads.sort((a, b) => b.createdAt - a.createdAt);
                    break;
                default: // 'modified'
                    filteredNotepads.sort((a, b) => b.lastModified - a.lastModified);
            }

            // Update count
            allCount.textContent = Object.keys(notepads).length;

            notepadsList.innerHTML = filteredNotepads.length > 0 ? filteredNotepads.map(notepad => `
                <div class="sidebar-item ${notepad.id === currentNotepadId ? 'active' : ''} flex items-center justify-between p-3 rounded-lg cursor-pointer group" data-notepad-id="${notepad.id}">
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                        <i class="fas fa-file-alt github-text-muted text-sm"></i>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                                <div class="notepad-name github-text font-medium truncate">${notepad.name}</div>
                                ${starredNotepads.has(notepad.id) ? '<i class="fas fa-star text-yellow-500 text-xs"></i>' : ''}
                            </div>
                            <input
                                class="rename-input hidden w-full px-2 py-1 github-bg border github-border rounded github-text text-sm font-medium focus:outline-none focus:ring-1 focus:ring-blue-500"
                                value="${notepad.name}"
                                data-notepad-id="${notepad.id}"
                            />
                            <div class="github-text-subtle text-xs">
                                ${getRelativeTime(notepad.lastModified)}
                            </div>
                        </div>
                    </div>
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                        <button class="star-toggle-btn p-1 hover:bg-gray-700 rounded text-xs ${starredNotepads.has(notepad.id) ? 'text-yellow-500' : 'github-text-muted'}" data-notepad-id="${notepad.id}" title="Star notepad">
                            <i class="fas fa-star"></i>
                        </button>
                        <button class="rename-btn p-1 hover:bg-gray-700 rounded text-xs github-text-muted" data-notepad-id="${notepad.id}" title="Rename notepad">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${Object.keys(notepads).length > 1 ? `
                            <button class="delete-btn p-1 hover:bg-red-600/20 rounded text-xs text-red-500" data-notepad-id="${notepad.id}" title="Delete notepad">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `).join('') : `
                <div class="text-center py-8 github-text-muted">
                    <i class="fas fa-search text-2xl mb-2"></i>
                    <p>No notepads found</p>
                    <p class="text-xs">Try adjusting your search or filters</p>
                </div>
            `;

            // Add event listeners
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    if (!e.target.closest('.rename-btn') && !e.target.closest('.delete-btn') && !e.target.closest('.rename-input')) {
                        const notepadId = item.dataset.notepadId;
                        switchToNotepad(notepadId);
                    }
                });
            });

            document.querySelectorAll('.rename-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const notepadId = btn.dataset.notepadId;
                    startInlineRename(notepadId);
                });
            });

            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const notepadId = btn.dataset.notepadId;
                    openDeleteModal(notepadId);
                });
            });

            // Add event listeners for inline rename inputs
            document.querySelectorAll('.rename-input').forEach(input => {
                input.addEventListener('blur', (e) => {
                    finishInlineRename(e.target);
                });

                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        finishInlineRename(e.target);
                    } else if (e.key === 'Escape') {
                        cancelInlineRename(e.target);
                    }
                });
            });

            // Add star toggle listeners
            document.querySelectorAll('.star-toggle-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const notepadId = btn.dataset.notepadId;
                    toggleStar(notepadId);
                });
            });
        }

        // GitHub-style relative time
        function getRelativeTime(timestamp) {
            const now = Date.now();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(diff / 3600000);
            const days = Math.floor(diff / 86400000);

            if (minutes < 1) return 'just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            if (days < 7) return `${days}d ago`;
            return new Date(timestamp).toLocaleDateString();
        }

        // Toggle star status
        function toggleStar(notepadId) {
            if (starredNotepads.has(notepadId)) {
                starredNotepads.delete(notepadId);
            } else {
                starredNotepads.add(notepadId);
            }

            // Save to localStorage
            localStorage.setItem('zoip-starred', JSON.stringify([...starredNotepads]));

            // Update star button in top bar if it's the current notepad
            if (notepadId === currentNotepadId) {
                updateStarButton();
            }

            // Re-render list
            renderNotepadsList();
        }

        // Update star button in top bar
        function updateStarButton() {
            const isStarred = starredNotepads.has(currentNotepadId);
            starBtn.innerHTML = `<i class="fa${isStarred ? 's' : 'r'} fa-star ${isStarred ? 'starred' : ''}"></i>`;
            starBtn.title = isStarred ? 'Unstar this notepad' : 'Star this notepad';
        }

        // Switch to a different notepad
        function switchToNotepad(notepadId) {
            // Save current notepad content before switching
            if (currentNotepadId && currentNotepadRef) {
                set(currentNotepadRef, notepad.value);
            }

            currentNotepadId = notepadId;
            updateReferences(notepadId);

            // Update UI
            const notepadData = notepads[notepadId];
            if (notepadData) {
                notepadTitle.value = notepadData.name;
                notepad.value = notepadData.content || '';
                updateStarButton();
            }

            // Re-initialize listeners for new notepad
            initializeNotepadListeners();
            renderNotepadsList(); // Re-render to update active state
        }

        // Create new notepad
        function createNewNotepad() {
            const newId = 'notepad_' + Date.now();
            const newNotepad = {
                id: newId,
                name: 'Untitled',
                content: '',
                createdAt: Date.now(),
                lastModified: Date.now()
            };

            set(ref(database, `notepads/${newId}`), newNotepad);
            switchToNotepad(newId);
        }

        // Start inline rename
        function startInlineRename(notepadId) {
            const sidebarItem = document.querySelector(`[data-notepad-id="${notepadId}"]`);
            const nameDiv = sidebarItem.querySelector('.notepad-name');
            const renameInput = sidebarItem.querySelector('.rename-input');

            // Hide name, show input
            nameDiv.classList.add('hidden');
            renameInput.classList.remove('hidden');
            renameInput.focus();
            renameInput.select();
        }

        // Finish inline rename
        function finishInlineRename(input) {
            const notepadId = input.dataset.notepadId;
            const newName = input.value.trim();

            if (newName && newName !== notepads[notepadId].name) {
                // Update Firebase
                set(ref(database, `notepads/${notepadId}/name`), newName);
                set(ref(database, `notepads/${notepadId}/lastModified`), Date.now());

                // Update current notepad title if it's the active one
                if (notepadId === currentNotepadId) {
                    notepadTitle.value = newName;
                }
            }

            // Hide input, show name
            const sidebarItem = input.closest('.sidebar-item');
            const nameDiv = sidebarItem.querySelector('.notepad-name');
            nameDiv.classList.remove('hidden');
            input.classList.add('hidden');
        }

        // Cancel inline rename
        function cancelInlineRename(input) {
            const notepadId = input.dataset.notepadId;

            // Reset input value
            input.value = notepads[notepadId].name;

            // Hide input, show name
            const sidebarItem = input.closest('.sidebar-item');
            const nameDiv = sidebarItem.querySelector('.notepad-name');
            nameDiv.classList.remove('hidden');
            input.classList.add('hidden');
        }

        // Open delete modal
        function openDeleteModal(notepadId) {
            // Prevent deleting the last notepad
            if (Object.keys(notepads).length <= 1) {
                return;
            }

            currentDeleteId = notepadId;
            deleteNotepadName.textContent = notepads[notepadId].name;
            deleteModal.classList.remove('hidden');
            deleteModal.classList.add('flex');
        }

        // Close delete modal
        function closeDeleteModal() {
            deleteModal.classList.add('hidden');
            deleteModal.classList.remove('flex');
            currentDeleteId = null;
        }

        // Delete notepad
        function deleteNotepad() {
            if (currentDeleteId && Object.keys(notepads).length > 1) {
                // If deleting the current notepad, switch to another one first
                if (currentDeleteId === currentNotepadId) {
                    const remainingNotepads = Object.keys(notepads).filter(id => id !== currentDeleteId);
                    if (remainingNotepads.length > 0) {
                        switchToNotepad(remainingNotepads[0]);
                    }
                }

                // Delete the notepad from Firebase
                set(ref(database, `notepads/${currentDeleteId}`), null);

                // Remove from local state
                delete notepads[currentDeleteId];

                closeDeleteModal();
            }
        }

        // Initialize user presence
        function initializeUser() {
            const userInfo = {
                id: userId,
                timestamp: serverTimestamp(),
                active: true
            };

            set(userRef, userInfo);

            // Remove user when they disconnect
            onDisconnect(userRef).remove();
        }

        // Update connection status
        function updateConnectionStatus(connected) {
            const statusClass = connected
                ? 'w-2 h-2 rounded-full bg-emerald-500 pulse-dot'
                : 'w-2 h-2 rounded-full bg-red-500 pulse-dot';
            const statusMessage = connected ? 'Connected' : 'Disconnected';

            connectionStatus.className = statusClass;
            statusText.textContent = statusMessage;

            // Update sync status
            syncStatus.textContent = connected ? 'Yes' : 'No';
            syncStatus.className = connected ? 'github-text font-medium' : 'text-red-400 font-medium';
        }

        // Initialize notepad-specific listeners
        function initializeNotepadListeners() {
            // Listen for notepad content changes
            onValue(currentNotepadRef, (snapshot) => {
                const content = snapshot.val();
                if (content !== null && content !== notepad.value) {
                    isUpdatingFromFirebase = true;
                    const cursorPosition = notepad.selectionStart;
                    notepad.value = content;
                    notepad.setSelectionRange(cursorPosition, cursorPosition);
                    isUpdatingFromFirebase = false;
                    updateLastSaved();
                }
            });

            // Listen for active users in current notepad
            onValue(currentUsersRef, (snapshot) => {
                const users = snapshot.val();
                const userCount = users ? Object.keys(users).length : 0;
                activeUsers.textContent = userCount;

                // Show typing indicator if more than one user
                if (userCount > 1 && Math.random() > 0.7) { // Simulate typing detection
                    showTypingIndicator();
                }
            });
        }

        // Listen for connection state
        const connectedRef = ref(database, '.info/connected');
        onValue(connectedRef, (snapshot) => {
            if (snapshot.val() === true) {
                updateConnectionStatus(true);
                initializeUser();
                initializeNotepads();
            } else {
                updateConnectionStatus(false);
            }
        });

        // Typing indicator
        function showTypingIndicator() {
            if (typingIndicator) {
                typingIndicator.classList.remove('hidden');
                typingIndicator.classList.add('flex');
                setTimeout(() => {
                    typingIndicator.classList.add('hidden');
                    typingIndicator.classList.remove('flex');
                }, 3000);
            }
        }



        // Save content to Firebase
        function saveContent() {
            if (!isUpdatingFromFirebase && notepad.value !== null && currentNotepadRef) {
                set(currentNotepadRef, notepad.value);
                set(ref(database, `notepads/${currentNotepadId}/lastModified`), Date.now());

                // Log activity
                if (currentActivityRef) {
                    const activityData = {
                        userId: userId,
                        action: 'edited the document',
                        timestamp: Date.now()
                    };
                    push(currentActivityRef, activityData);
                }

                updateLastSaved();
            }
        }

        // Update last saved timestamp
        function updateLastSaved() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            lastSaved.textContent = timeString;
        }

        // Debounced save function
        function debouncedSave() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(saveContent, 500);
        }

        // Save notepad title
        function saveNotepadTitle() {
            if (currentNotepadId && notepadTitle.value.trim()) {
                const newName = notepadTitle.value.trim();
                set(ref(database, `notepads/${currentNotepadId}/name`), newName);
                set(ref(database, `notepads/${currentNotepadId}/lastModified`), Date.now());
            }
        }

        // Event listeners
        notepad.addEventListener('input', debouncedSave);
        notepad.addEventListener('paste', () => setTimeout(debouncedSave, 10));

        // Notepad title editing
        notepadTitle.addEventListener('blur', saveNotepadTitle);
        notepadTitle.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                notepadTitle.blur();
            }
        });

        // Add new notepad button
        addNotepadBtn.addEventListener('click', createNewNotepad);

        // GitHub-style search
        searchNotepads.addEventListener('input', (e) => {
            searchQuery = e.target.value;
            renderNotepadsList();
        });

        // Filter tabs
        filterAll.addEventListener('click', () => setFilter('all'));
        filterRecent.addEventListener('click', () => setFilter('recent'));
        filterStarred.addEventListener('click', () => setFilter('starred'));

        // Sort dropdown
        sortNotepads.addEventListener('change', (e) => {
            currentSort = e.target.value;
            renderNotepadsList();
        });

        // Star button in top bar
        starBtn.addEventListener('click', () => {
            toggleStar(currentNotepadId);
        });

        // More actions dropdown
        moreActionsBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            moreActionsMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            moreActionsMenu.classList.add('hidden');
        });

        // Export functionality
        exportBtn.addEventListener('click', () => {
            const content = notepad.value;
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${notepads[currentNotepadId]?.name || 'notepad'}.md`;
            a.click();
            URL.revokeObjectURL(url);
            moreActionsMenu.classList.add('hidden');
        });

        // Share functionality
        shareBtn.addEventListener('click', async () => {
            const url = window.location.href;
            try {
                await navigator.clipboard.writeText(url);
                // Show temporary success message
                const originalText = shareBtn.innerHTML;
                shareBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
                setTimeout(() => {
                    shareBtn.innerHTML = originalText;
                }, 2000);
            } catch (err) {
                console.error('Failed to copy URL:', err);
            }
            moreActionsMenu.classList.add('hidden');
        });

        // Set filter function
        function setFilter(filter) {
            currentFilter = filter;

            // Update active tab
            document.querySelectorAll('.filter-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(`filter${filter.charAt(0).toUpperCase() + filter.slice(1)}`).classList.add('active');

            renderNotepadsList();
        }



        // Delete modal events
        confirmDelete.addEventListener('click', deleteNotepad);
        cancelDelete.addEventListener('click', closeDeleteModal);

        // Close delete modal on backdrop click
        deleteModal.addEventListener('click', (e) => {
            if (e.target === deleteModal) {
                closeDeleteModal();
            }
        });

        // Keyboard shortcuts for delete modal
        document.addEventListener('keydown', (e) => {
            if (deleteModal.classList.contains('flex')) {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                } else if (e.key === 'Enter') {
                    deleteNotepad();
                }
            }
        });

        // Clear button with GitHub-style confirmation
        clearBtn.addEventListener('click', () => {
            // Create custom confirmation dialog
            const confirmDialog = document.createElement('div');
            confirmDialog.className = 'fixed inset-0 bg-black/50 apple-blur flex items-center justify-center z-50';
            confirmDialog.innerHTML = `
                <div class="github-bg-subtle border github-border rounded-xl p-6 w-96 mx-4 apple-shadow-lg">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold github-text mb-2">Clear Notepad</h3>
                        <p class="github-text-muted mb-6">Are you sure you want to clear this notepad? This action cannot be undone.</p>
                        <div class="flex space-x-3">
                            <button id="cancelClear" class="flex-1 px-4 py-2 github-text-muted hover:github-text rounded-lg apple-button">
                                Cancel
                            </button>
                            <button id="confirmClear" class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg apple-button">
                                Clear
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(confirmDialog);

            // Handle confirmation
            document.getElementById('confirmClear').addEventListener('click', () => {
                notepad.value = '';
                saveContent();

                // Log activity
                if (currentActivityRef) {
                    const activityData = {
                        userId: userId,
                        action: 'cleared the document',
                        timestamp: Date.now()
                    };
                    push(currentActivityRef, activityData);
                }

                document.body.removeChild(confirmDialog);
            });

            // Handle cancellation
            document.getElementById('cancelClear').addEventListener('click', () => {
                document.body.removeChild(confirmDialog);
            });

            // Close on backdrop click
            confirmDialog.addEventListener('click', (e) => {
                if (e.target === confirmDialog) {
                    document.body.removeChild(confirmDialog);
                }
            });
        });

        // Update user activity on focus
        notepad.addEventListener('focus', () => {
            if (currentActivityRef) {
                const activityData = {
                    userId: userId,
                    action: 'started editing',
                    timestamp: Date.now()
                };
                push(currentActivityRef, activityData);
            }
        });

        // Cleanup old activity entries (keep only last 50)
        setInterval(() => {
            if (currentActivityRef) {
                onValue(currentActivityRef, (snapshot) => {
                    const activities = snapshot.val();
                    if (activities) {
                        const activityArray = Object.entries(activities);
                        if (activityArray.length > 50) {
                            // Remove oldest entries
                            const toRemove = activityArray
                                .sort((a, b) => a[1].timestamp - b[1].timestamp)
                                .slice(0, activityArray.length - 50);

                            toRemove.forEach(([key]) => {
                                set(ref(database, `notepads/${currentNotepadId}/activity/${key}`), null);
                            });
                        }
                    }
                }, { once: true });
            }
        }, 60000); // Check every minute

        console.log('ZOIP initialized with user ID:', userId);
    </script>
</body>
</html>